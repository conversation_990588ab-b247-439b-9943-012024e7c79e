-- 字段映射迁移脚本
-- 专门处理源表字段少于目标表的情况
-- 从Hyun数据库迁移到HyunCoreBase数据库

USE HyunCoreBase;
GO

DECLARE @FixedUserId NVARCHAR(50) = '592123693924485';
DECLARE @FixedUnitId NVARCHAR(50) = '592123124052101';

PRINT '开始字段映射数据迁移...';
PRINT '源数据库: Hyun (字段较少)';
PRINT '目标数据库: HyunCoreBase (字段较多)';
PRINT '固定用户ID: ' + @FixedUserId;
PRINT '固定单位ID: ' + @FixedUnitId;
PRINT '';

-- ========================================
-- 1. 迁移 dc_PurchaseList 表
-- ========================================
PRINT '正在迁移 dc_PurchaseList 表...';

BEGIN TRY
    -- 先检查源表结构
    PRINT '源表字段: ';
    SELECT '  ' + COLUMN_NAME + ' (' + DATA_TYPE + ')' AS 字段信息
    FROM Hyun.INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'dc_PurchaseList' AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
    
    PRINT '';
    PRINT '开始插入数据...';
    
    -- 使用具体的字段映射
    INSERT INTO dc_PurchaseList (
        -- 从源表直接映射的字段
        Id,
        PurchaseNo,
        PurchaseDate,
        SupplierId,
        SupplierName,
        TotalAmount,
        Status,
        CreateTime,
        UpdateTime,
        Remark,
        
        -- 使用固定值的字段
        CreateUserId,
        UpdateUserId,
        UnitId
        
        -- 注意：如果目标表还有其他字段，需要在这里添加并在SELECT中提供默认值
    )
    SELECT 
        -- 从源表获取的字段
        src.Id,
        ISNULL(src.PurchaseNo, '') AS PurchaseNo,
        ISNULL(src.PurchaseDate, GETDATE()) AS PurchaseDate,
        ISNULL(src.SupplierId, '') AS SupplierId,
        ISNULL(src.SupplierName, '') AS SupplierName,
        ISNULL(src.TotalAmount, 0) AS TotalAmount,
        ISNULL(src.Status, 0) AS Status,
        ISNULL(src.CreateTime, GETDATE()) AS CreateTime,
        ISNULL(src.UpdateTime, GETDATE()) AS UpdateTime,
        ISNULL(src.Remark, '') AS Remark,
        
        -- 固定值字段
        @FixedUserId AS CreateUserId,
        @FixedUserId AS UpdateUserId,
        @FixedUnitId AS UnitId
        
    FROM Hyun.dbo.dc_PurchaseList src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseList target 
        WHERE target.Id = src.Id
    );
    
    PRINT 'dc_PurchaseList 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseList 表迁移失败: ' + ERROR_MESSAGE();
    PRINT '错误详情: ' + ERROR_MESSAGE();
    PRINT '错误行号: ' + CAST(ERROR_LINE() AS NVARCHAR(10));
END CATCH

PRINT '';

-- ========================================
-- 2. 迁移 dc_PurchaseOrder 表
-- ========================================
PRINT '正在迁移 dc_PurchaseOrder 表...';

BEGIN TRY
    -- 先检查源表结构
    PRINT '源表字段: ';
    SELECT '  ' + COLUMN_NAME + ' (' + DATA_TYPE + ')' AS 字段信息
    FROM Hyun.INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'dc_PurchaseOrder' AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
    
    PRINT '';
    PRINT '开始插入数据...';
    
    INSERT INTO dc_PurchaseOrder (
        -- 从源表直接映射的字段
        Id,
        OrderNo,
        OrderDate,
        SupplierId,
        SupplierName,
        TotalAmount,
        Status,
        CreateTime,
        UpdateTime,
        Remark,
        PurchaseListId,
        
        -- 使用固定值的字段
        CreateUserId,
        UpdateUserId,
        UnitId
    )
    SELECT 
        -- 从源表获取的字段
        src.Id,
        ISNULL(src.OrderNo, '') AS OrderNo,
        ISNULL(src.OrderDate, GETDATE()) AS OrderDate,
        ISNULL(src.SupplierId, '') AS SupplierId,
        ISNULL(src.SupplierName, '') AS SupplierName,
        ISNULL(src.TotalAmount, 0) AS TotalAmount,
        ISNULL(src.Status, 0) AS Status,
        ISNULL(src.CreateTime, GETDATE()) AS CreateTime,
        ISNULL(src.UpdateTime, GETDATE()) AS UpdateTime,
        ISNULL(src.Remark, '') AS Remark,
        ISNULL(src.PurchaseListId, '') AS PurchaseListId,
        
        -- 固定值字段
        @FixedUserId AS CreateUserId,
        @FixedUserId AS UpdateUserId,
        @FixedUnitId AS UnitId
        
    FROM Hyun.dbo.dc_PurchaseOrder src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseOrder target 
        WHERE target.Id = src.Id
    );
    
    PRINT 'dc_PurchaseOrder 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseOrder 表迁移失败: ' + ERROR_MESSAGE();
    PRINT '错误详情: ' + ERROR_MESSAGE();
    PRINT '错误行号: ' + CAST(ERROR_LINE() AS NVARCHAR(10));
END CATCH

PRINT '';

-- ========================================
-- 3. 迁移 dc_PurchaseApproval 表
-- ========================================
PRINT '正在迁移 dc_PurchaseApproval 表...';

BEGIN TRY
    -- 先检查源表结构
    PRINT '源表字段: ';
    SELECT '  ' + COLUMN_NAME + ' (' + DATA_TYPE + ')' AS 字段信息
    FROM Hyun.INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'dc_PurchaseApproval' AND TABLE_SCHEMA = 'dbo'
    ORDER BY ORDINAL_POSITION;
    
    PRINT '';
    PRINT '开始插入数据...';
    
    INSERT INTO dc_PurchaseApproval (
        -- 从源表直接映射的字段
        Id,
        ApprovalNo,
        ApprovalDate,
        PurchaseOrderId,
        ApprovalStatus,
        ApprovalComment,
        CreateTime,
        UpdateTime,
        Remark,
        
        -- 使用固定值的字段
        CreateUserId,
        UpdateUserId,
        UnitId
    )
    SELECT 
        -- 从源表获取的字段
        src.Id,
        ISNULL(src.ApprovalNo, '') AS ApprovalNo,
        ISNULL(src.ApprovalDate, GETDATE()) AS ApprovalDate,
        ISNULL(src.PurchaseOrderId, '') AS PurchaseOrderId,
        ISNULL(src.ApprovalStatus, 0) AS ApprovalStatus,
        ISNULL(src.ApprovalComment, '') AS ApprovalComment,
        ISNULL(src.CreateTime, GETDATE()) AS CreateTime,
        ISNULL(src.UpdateTime, GETDATE()) AS UpdateTime,
        ISNULL(src.Remark, '') AS Remark,
        
        -- 固定值字段
        @FixedUserId AS CreateUserId,
        @FixedUserId AS UpdateUserId,
        @FixedUnitId AS UnitId
        
    FROM Hyun.dbo.dc_PurchaseApproval src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseApproval target 
        WHERE target.Id = src.Id
    );
    
    PRINT 'dc_PurchaseApproval 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseApproval 表迁移失败: ' + ERROR_MESSAGE();
    PRINT '错误详情: ' + ERROR_MESSAGE();
    PRINT '错误行号: ' + CAST(ERROR_LINE() AS NVARCHAR(10));
END CATCH

PRINT '';
PRINT '========================================';
PRINT '所有数据迁移完成！';
PRINT '========================================';

-- 显示迁移后的统计信息
PRINT '';
PRINT '迁移后统计信息:';
PRINT 'dc_PurchaseList 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseList) AS NVARCHAR(10));
PRINT 'dc_PurchaseOrder 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseOrder) AS NVARCHAR(10));
PRINT 'dc_PurchaseApproval 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseApproval) AS NVARCHAR(10));

GO
