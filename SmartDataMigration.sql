-- 智能数据迁移脚本：使用INSERT INTO SELECT，自动处理缺失字段
-- 从Hyun数据库迁移数据到HyunCoreBase数据库
-- 固定用户ID: 592123693924485
-- 固定单位ID: 592123124052101

USE HyunCoreBase;
GO

-- 声明变量
DECLARE @FixedUserId NVARCHAR(50) = '592123693924485';
DECLARE @FixedUnitId NVARCHAR(50) = '592123124052101';
DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @TargetColumns NVARCHAR(MAX);
DECLARE @SourceColumns NVARCHAR(MAX);
DECLARE @RowCount INT;

PRINT '开始智能数据迁移...';

-- 要迁移的表列表
DECLARE table_cursor CURSOR FOR
SELECT 'dc_PurchaseList' AS TableName
UNION ALL
SELECT 'dc_PurchaseOrder'
UNION ALL
SELECT 'dc_PurchaseApproval';

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '正在处理表: ' + @TableName;
    
    -- 检查源表和目标表是否都存在
    IF EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
       AND EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
    BEGIN
        -- 获取目标表的所有列（排除Identity列）
        SELECT @TargetColumns = STRING_AGG(COLUMN_NAME, ', ')
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = @TableName 
          AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
          AND TABLE_SCHEMA = 'dbo'
        ORDER BY ORDINAL_POSITION;

        -- 构建源表的SELECT列表，处理缺失字段和特殊字段
        WITH TargetCols AS (
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = @TableName 
              AND COLUMNPROPERTY(OBJECT_ID(TABLE_SCHEMA + '.' + TABLE_NAME), COLUMN_NAME, 'IsIdentity') = 0
              AND TABLE_SCHEMA = 'dbo'
        ),
        SourceCols AS (
            SELECT COLUMN_NAME
            FROM Hyun.INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = @TableName
              AND TABLE_SCHEMA = 'dbo'
        )
        SELECT @SourceColumns = STRING_AGG(
            CASE 
                -- 处理用户ID字段
                WHEN tc.COLUMN_NAME LIKE '%UserId' OR tc.COLUMN_NAME IN ('CreateUserId', 'UpdateUserId', 'CreatedBy', 'UpdatedBy') 
                THEN '''' + @FixedUserId + ''' AS ' + tc.COLUMN_NAME
                
                -- 处理单位ID字段
                WHEN tc.COLUMN_NAME LIKE '%UnitId' OR tc.COLUMN_NAME IN ('OrganizationId', 'DepartmentId')
                THEN '''' + @FixedUnitId + ''' AS ' + tc.COLUMN_NAME
                
                -- 如果源表中存在该字段，直接使用
                WHEN sc.COLUMN_NAME IS NOT NULL
                THEN 'src.' + tc.COLUMN_NAME
                
                -- 如果源表中不存在该字段，使用默认值
                ELSE 
                    CASE 
                        -- 数值类型默认值
                        WHEN tc.DATA_TYPE IN ('int', 'bigint', 'smallint', 'tinyint', 'bit') THEN '0 AS ' + tc.COLUMN_NAME
                        WHEN tc.DATA_TYPE IN ('decimal', 'numeric', 'float', 'real', 'money', 'smallmoney') THEN '0.0 AS ' + tc.COLUMN_NAME
                        
                        -- 字符串类型默认值
                        WHEN tc.DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar', 'text', 'ntext') THEN ''''' AS ' + tc.COLUMN_NAME
                        
                        -- 日期时间类型默认值
                        WHEN tc.DATA_TYPE IN ('datetime', 'datetime2', 'smalldatetime', 'date', 'time') THEN 'GETDATE() AS ' + tc.COLUMN_NAME
                        
                        -- GUID类型默认值
                        WHEN tc.DATA_TYPE = 'uniqueidentifier' THEN 'NEWID() AS ' + tc.COLUMN_NAME
                        
                        -- 其他类型，如果允许NULL则用NULL，否则用空字符串
                        WHEN tc.IS_NULLABLE = 'YES' THEN 'NULL AS ' + tc.COLUMN_NAME
                        ELSE ''''' AS ' + tc.COLUMN_NAME
                    END
            END, ', ')
        FROM TargetCols tc
        LEFT JOIN SourceCols sc ON tc.COLUMN_NAME = sc.COLUMN_NAME
        ORDER BY tc.ORDINAL_POSITION;

        -- 构建完整的INSERT语句
        SET @SQL = '
        INSERT INTO ' + @TableName + ' (' + @TargetColumns + ')
        SELECT ' + @SourceColumns + '
        FROM Hyun.dbo.' + @TableName + ' src
        WHERE NOT EXISTS (
            SELECT 1 FROM ' + @TableName + ' target 
            WHERE target.Id = src.Id
        );';

        -- 执行迁移
        BEGIN TRY
            PRINT '执行SQL:';
            PRINT @SQL;
            PRINT '';
            
            EXEC sp_executesql @SQL;
            SET @RowCount = @@ROWCOUNT;
            PRINT @TableName + ' 表迁移完成，新增行数: ' + CAST(@RowCount AS NVARCHAR(10));
            PRINT '';
        END TRY
        BEGIN CATCH
            PRINT '迁移表 ' + @TableName + ' 时发生错误:';
            PRINT ERROR_MESSAGE();
            PRINT '错误行号: ' + CAST(ERROR_LINE() AS NVARCHAR(10));
            PRINT '';
        END CATCH
    END
    ELSE
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM Hyun.INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
            PRINT '源表 Hyun.dbo.' + @TableName + ' 不存在，跳过迁移';
        
        IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName)
            PRINT '目标表 HyunCoreBase.dbo.' + @TableName + ' 不存在，跳过迁移';
        
        PRINT '';
    END
    
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

PRINT '========================================';
PRINT '所有数据迁移完成！';
PRINT '========================================';
GO
