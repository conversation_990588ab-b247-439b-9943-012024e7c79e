@echo off
chcp 65001 >nul
echo ========================================
echo 数据迁移工具 - INSERT INTO SELECT方式
echo ========================================
echo.
echo 服务器: ***********
echo 源数据库: Hyun
echo 目标数据库: HyunCoreBase
echo 固定用户ID: 592123693924485
echo 固定单位ID: 592123124052101
echo.

:menu
echo 请选择要执行的脚本：
echo 1. 显示表结构对比 (ShowTableStructure.sql) - 推荐先运行
echo 2. 检查并生成脚本 (InspectAndGenerate.sql)
echo 3. 智能字段映射迁移 (FieldMappingMigration.sql)
echo 4. 智能版迁移脚本 (SmartDataMigration.sql)
echo 5. 分析表字段差异 (AnalyzeTableDifferences.sql)
echo 6. 生成INSERT脚本工具 (GenerateInsertScript.sql)
echo 7. 退出
echo.
echo 建议流程：先运行选项1查看表结构，再根据实际情况选择其他选项
echo.
set /p choice=请输入选择 (1-7):

if "%choice%"=="1" goto showstructure
if "%choice%"=="2" goto inspect
if "%choice%"=="3" goto fieldmapping
if "%choice%"=="4" goto smart
if "%choice%"=="5" goto analyze
if "%choice%"=="6" goto generate
if "%choice%"=="7" goto exit
echo 无效选择，请重新输入
goto menu

:showstructure
echo.
echo 正在显示表结构对比...
echo 此工具将显示源表和目标表的实际字段结构
sqlcmd -S *********** -U hyun -P hyun -i ShowTableStructure.sql
goto result

:inspect
echo.
echo 正在检查表结构并生成脚本...
echo 此工具将分析表结构并生成INSERT脚本模板
sqlcmd -S *********** -U hyun -P hyun -i InspectAndGenerate.sql
goto result

:fieldmapping
echo.
echo 正在执行智能字段映射迁移...
echo 此脚本会动态分析表结构并生成正确的INSERT语句
sqlcmd -S *********** -U hyun -P hyun -i FieldMappingMigration.sql
goto result

:smart
echo.
echo 正在执行智能版迁移脚本...
sqlcmd -S *********** -U hyun -P hyun -i SmartDataMigration.sql
goto result

:analyze
echo.
echo 正在分析表字段差异...
echo 此工具将显示源表和目标表的字段对比
sqlcmd -S *********** -U hyun -P hyun -i AnalyzeTableDifferences.sql
goto result

:generate
echo.
echo 正在执行脚本生成工具...
sqlcmd -S *********** -U hyun -P hyun -i GenerateInsertScript.sql
goto result

:result
echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo 执行完成！
    echo ========================================
) else (
    echo ========================================
    echo 执行失败！错误代码: %ERRORLEVEL%
    echo ========================================
)
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:exit
echo 再见！
pause
