@echo off
chcp 65001 >nul
echo ========================================
echo 数据迁移工具 - INSERT INTO SELECT方式
echo ========================================
echo.
echo 服务器: ***********
echo 源数据库: Hyun
echo 目标数据库: HyunCoreBase
echo 固定用户ID: 592123693924485
echo 固定单位ID: 592123124052101
echo.

:menu
echo 请选择要执行的脚本：
echo 1. 简化版迁移脚本 (SimpleInsertMigration.sql)
echo 2. 智能版迁移脚本 (SmartDataMigration.sql)
echo 3. 生成INSERT脚本工具 (GenerateInsertScript.sql)
echo 4. 退出
echo.
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto simple
if "%choice%"=="2" goto smart
if "%choice%"=="3" goto generate
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
goto menu

:simple
echo.
echo 正在执行简化版迁移脚本...
sqlcmd -S *********** -U hyun -P hyun -i SimpleInsertMigration.sql
goto result

:smart
echo.
echo 正在执行智能版迁移脚本...
sqlcmd -S *********** -U hyun -P hyun -i SmartDataMigration.sql
goto result

:generate
echo.
echo 正在执行脚本生成工具...
sqlcmd -S *********** -U hyun -P hyun -i GenerateInsertScript.sql
goto result

:result
echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo 执行完成！
    echo ========================================
) else (
    echo ========================================
    echo 执行失败！错误代码: %ERRORLEVEL%
    echo ========================================
)
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:exit
echo 再见！
pause
