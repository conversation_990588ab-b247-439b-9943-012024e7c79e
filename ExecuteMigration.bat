@echo off
chcp 65001 >nul
echo ========================================
echo 数据迁移工具 - INSERT INTO SELECT方式
echo ========================================
echo.
echo 服务器: ***********
echo 源数据库: Hyun
echo 目标数据库: HyunCoreBase
echo 固定用户ID: 592123693924485
echo 固定单位ID: 592123124052101
echo.

:menu
echo 请选择要执行的脚本：
echo 1. 字段映射迁移脚本 (FieldMappingMigration.sql) - 推荐
echo 2. 智能版迁移脚本 (SmartDataMigration.sql)
echo 3. 简化版迁移脚本 (SimpleInsertMigration.sql)
echo 4. 分析表字段差异 (AnalyzeTableDifferences.sql)
echo 5. 生成INSERT脚本工具 (GenerateInsertScript.sql)
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6):

if "%choice%"=="1" goto fieldmapping
if "%choice%"=="2" goto smart
if "%choice%"=="3" goto simple
if "%choice%"=="4" goto analyze
if "%choice%"=="5" goto generate
if "%choice%"=="6" goto exit
echo 无效选择，请重新输入
goto menu

:fieldmapping
echo.
echo 正在执行字段映射迁移脚本...
echo 此脚本专门处理源表字段少于目标表的情况
sqlcmd -S *********** -U hyun -P hyun -i FieldMappingMigration.sql
goto result

:smart
echo.
echo 正在执行智能版迁移脚本...
sqlcmd -S *********** -U hyun -P hyun -i SmartDataMigration.sql
goto result

:simple
echo.
echo 正在执行简化版迁移脚本...
sqlcmd -S *********** -U hyun -P hyun -i SimpleInsertMigration.sql
goto result

:analyze
echo.
echo 正在分析表字段差异...
echo 此工具将显示源表和目标表的字段对比
sqlcmd -S *********** -U hyun -P hyun -i AnalyzeTableDifferences.sql
goto result

:generate
echo.
echo 正在执行脚本生成工具...
sqlcmd -S *********** -U hyun -P hyun -i GenerateInsertScript.sql
goto result

:result
echo.
if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo 执行完成！
    echo ========================================
) else (
    echo ========================================
    echo 执行失败！错误代码: %ERRORLEVEL%
    echo ========================================
)
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:exit
echo 再见！
pause
