# 数据迁移工具

## 概述
这是一套用于从Hyun数据库迁移数据到HyunCoreBase数据库的工具集。主要功能包括：
- 迁移三个表：`dc_PurchaseList`、`dc_PurchaseOrder`、`dc_PurchaseApproval`
- 自动处理用户ID和单位ID的替换
- 智能处理缺失字段，使用合适的默认值
- 避免重复数据插入

## 配置信息
- **数据库服务器**: ***********
- **源数据库**: Hyun
- **目标数据库**: HyunCoreBase
- **用户名**: hyun
- **密码**: hyun
- **固定用户ID**: 592123693924485
- **固定单位ID**: 592123124052101

## 文件说明

### 1. SimpleInsertMigration.sql
**简化版迁移脚本**
- 直接针对三个指定表编写的INSERT INTO SELECT语句
- 使用ISNULL函数处理空值
- 为不同数据类型提供合适的默认值：
  - 数值类型：0
  - 字符串类型：''（空字符串）
  - 日期时间类型：GETDATE()
  - GUID类型：NEWID()

### 2. SmartDataMigration.sql
**智能版迁移脚本**
- 动态分析表结构
- 自动检测源表和目标表的字段差异
- 智能生成INSERT语句
- 更灵活，适应表结构变化

### 3. GenerateInsertScript.sql
**脚本生成工具**
- 分析目标表结构
- 自动生成INSERT INTO SELECT脚本
- 输出可直接执行的SQL代码
- 便于自定义和调试

### 4. ExecuteMigration.bat
**批处理执行工具**
- 提供菜单选择不同的迁移方式
- 自动调用sqlcmd执行SQL脚本
- 显示执行结果和错误信息

## 使用方法

### 方法一：使用批处理文件（推荐）
1. 双击运行 `ExecuteMigration.bat`
2. 根据菜单选择要执行的脚本
3. 等待执行完成

### 方法二：直接执行SQL脚本
1. 打开SQL Server Management Studio
2. 连接到数据库服务器 ***********
3. 选择要执行的SQL脚本文件
4. 执行脚本

### 方法三：使用命令行
```cmd
sqlcmd -S *********** -U hyun -P hyun -i SimpleInsertMigration.sql
```

## 默认值规则

脚本会根据字段的数据类型自动分配默认值：

| 数据类型 | 默认值 |
|---------|--------|
| int, bigint, smallint, tinyint, bit | 0 |
| decimal, numeric, float, real, money, smallmoney | 0.0 |
| varchar, nvarchar, char, nchar, text, ntext | '' (空字符串) |
| datetime, datetime2, smalldatetime, date, time | GETDATE() |
| uniqueidentifier | NEWID() |
| 其他类型 | NULL 或 '' |

## 特殊字段处理

### 用户ID字段
以下字段会被替换为固定值 `592123693924485`：
- 包含 "UserId" 的字段
- CreateUserId
- UpdateUserId
- CreatedBy
- UpdatedBy

### 单位ID字段
以下字段会被替换为固定值 `592123124052101`：
- 包含 "UnitId" 的字段
- OrganizationId
- DepartmentId

## 注意事项

1. **备份数据**：执行迁移前请备份目标数据库
2. **权限检查**：确保数据库用户有足够的权限
3. **网络连接**：确保能够连接到数据库服务器
4. **表结构**：确保目标表已存在且结构正确
5. **重复执行**：脚本使用NOT EXISTS避免重复插入，可以安全重复执行

## 故障排除

### 常见错误
1. **连接失败**：检查服务器地址、用户名、密码
2. **表不存在**：确认源表和目标表都存在
3. **权限不足**：确保用户有SELECT和INSERT权限
4. **数据类型不匹配**：检查表结构是否一致

### 调试方法
1. 使用 `GenerateInsertScript.sql` 生成脚本并检查
2. 先在测试环境执行
3. 逐个表执行，而不是批量执行

## 扩展使用

如果需要迁移其他表，可以：
1. 修改脚本中的表名列表
2. 调整特殊字段的处理规则
3. 根据需要修改默认值规则

## 技术支持

如有问题，请检查：
1. SQL Server版本兼容性
2. 数据库连接配置
3. 表结构差异
4. 数据类型兼容性
