-- 简化的INSERT INTO SELECT数据迁移脚本
-- 从Hyun数据库迁移数据到HyunCoreBase数据库
-- 固定用户ID: 592123693924485
-- 固定单位ID: 592123124052101

USE HyunCoreBase;
GO

DECLARE @FixedUserId NVARCHAR(50) = '592123693924485';
DECLARE @FixedUnitId NVARCHAR(50) = '592123124052101';

PRINT '开始数据迁移...';
PRINT '固定用户ID: ' + @FixedUserId;
PRINT '固定单位ID: ' + @FixedUnitId;
PRINT '';

-- ========================================
-- 1. 迁移 dc_PurchaseList 表
-- ========================================
PRINT '正在迁移 dc_PurchaseList 表...';

BEGIN TRY
    INSERT INTO dc_PurchaseList (
        Id, 
        PurchaseNo, 
        PurchaseDate, 
        SupplierId, 
        SupplierName, 
        TotalAmount, 
        Status,
        CreateUserId,
        CreateTime,
        UpdateUserId,
        UpdateTime,
        UnitId,
        Remark
    )
    SELECT 
        ISNULL(src.Id, NEWID()) AS Id,
        ISNULL(src.PurchaseNo, '') AS PurchaseNo,
        ISNULL(src.PurchaseDate, GETDATE()) AS PurchaseDate,
        ISNULL(src.SupplierId, '') AS SupplierId,
        ISNULL(src.SupplierName, '') AS SupplierName,
        ISNULL(src.TotalAmount, 0) AS TotalAmount,
        ISNULL(src.Status, 0) AS Status,
        @FixedUserId AS CreateUserId,
        ISNULL(src.CreateTime, GETDATE()) AS CreateTime,
        @FixedUserId AS UpdateUserId,
        ISNULL(src.UpdateTime, GETDATE()) AS UpdateTime,
        @FixedUnitId AS UnitId,
        ISNULL(src.Remark, '') AS Remark
    FROM Hyun.dbo.dc_PurchaseList src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseList target 
        WHERE target.Id = src.Id
    );
    
    PRINT 'dc_PurchaseList 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseList 表迁移失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- ========================================
-- 2. 迁移 dc_PurchaseOrder 表
-- ========================================
PRINT '正在迁移 dc_PurchaseOrder 表...';

BEGIN TRY
    INSERT INTO dc_PurchaseOrder (
        Id,
        OrderNo,
        OrderDate,
        SupplierId,
        SupplierName,
        TotalAmount,
        Status,
        CreateUserId,
        CreateTime,
        UpdateUserId,
        UpdateTime,
        UnitId,
        Remark,
        PurchaseListId
    )
    SELECT 
        ISNULL(src.Id, NEWID()) AS Id,
        ISNULL(src.OrderNo, '') AS OrderNo,
        ISNULL(src.OrderDate, GETDATE()) AS OrderDate,
        ISNULL(src.SupplierId, '') AS SupplierId,
        ISNULL(src.SupplierName, '') AS SupplierName,
        ISNULL(src.TotalAmount, 0) AS TotalAmount,
        ISNULL(src.Status, 0) AS Status,
        @FixedUserId AS CreateUserId,
        ISNULL(src.CreateTime, GETDATE()) AS CreateTime,
        @FixedUserId AS UpdateUserId,
        ISNULL(src.UpdateTime, GETDATE()) AS UpdateTime,
        @FixedUnitId AS UnitId,
        ISNULL(src.Remark, '') AS Remark,
        ISNULL(src.PurchaseListId, '') AS PurchaseListId
    FROM Hyun.dbo.dc_PurchaseOrder src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseOrder target 
        WHERE target.Id = src.Id
    );
    
    PRINT 'dc_PurchaseOrder 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseOrder 表迁移失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- ========================================
-- 3. 迁移 dc_PurchaseApproval 表
-- ========================================
PRINT '正在迁移 dc_PurchaseApproval 表...';

BEGIN TRY
    INSERT INTO dc_PurchaseApproval (
        Id,
        ApprovalNo,
        ApprovalDate,
        PurchaseOrderId,
        ApprovalStatus,
        ApprovalComment,
        CreateUserId,
        CreateTime,
        UpdateUserId,
        UpdateTime,
        UnitId,
        Remark
    )
    SELECT 
        ISNULL(src.Id, NEWID()) AS Id,
        ISNULL(src.ApprovalNo, '') AS ApprovalNo,
        ISNULL(src.ApprovalDate, GETDATE()) AS ApprovalDate,
        ISNULL(src.PurchaseOrderId, '') AS PurchaseOrderId,
        ISNULL(src.ApprovalStatus, 0) AS ApprovalStatus,
        ISNULL(src.ApprovalComment, '') AS ApprovalComment,
        @FixedUserId AS CreateUserId,
        ISNULL(src.CreateTime, GETDATE()) AS CreateTime,
        @FixedUserId AS UpdateUserId,
        ISNULL(src.UpdateTime, GETDATE()) AS UpdateTime,
        @FixedUnitId AS UnitId,
        ISNULL(src.Remark, '') AS Remark
    FROM Hyun.dbo.dc_PurchaseApproval src
    WHERE NOT EXISTS (
        SELECT 1 FROM dc_PurchaseApproval target 
        WHERE target.Id = src.Id
    );
    
    PRINT 'dc_PurchaseApproval 表迁移完成，新增行数: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END TRY
BEGIN CATCH
    PRINT 'dc_PurchaseApproval 表迁移失败: ' + ERROR_MESSAGE();
END CATCH

PRINT '';
PRINT '========================================';
PRINT '所有数据迁移完成！';
PRINT '========================================';

-- 显示迁移后的统计信息
PRINT '';
PRINT '迁移后统计信息:';
PRINT 'dc_PurchaseList 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseList) AS NVARCHAR(10));
PRINT 'dc_PurchaseOrder 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseOrder) AS NVARCHAR(10));
PRINT 'dc_PurchaseApproval 总记录数: ' + CAST((SELECT COUNT(*) FROM dc_PurchaseApproval) AS NVARCHAR(10));

GO
